// Facebook Pixel and CAPI Tracking Utilities
import { v4 as uuidv4 } from 'uuid'

// Extend Window interface for fbq
declare global {
  interface Window {
    fbq: any
  }
}

// Facebook Pixel Event Names
export const FB_EVENTS = {
  PAGE_VIEW: 'PageView',
  CONTACT: 'Contact'
} as const

// Generate unique event ID for deduplication
export function generateEventId(): string {
  return uuidv4()
}

// Hash email for privacy (SHA-256)
export async function hashEmail(email: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(email.toLowerCase().trim())
  const hash = await crypto.subtle.digest('SHA-256', data)
  return Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
}

// Hash phone number for privacy (SHA-256)
export async function hashPhone(phone: string): Promise<string> {
  const encoder = new TextEncoder()
  // Remove all non-digits and normalize
  const normalizedPhone = phone.replace(/\D/g, '')
  const data = encoder.encode(normalizedPhone)
  const hash = await crypto.subtle.digest('SHA-256', data)
  return Array.from(new Uint8Array(hash))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
}

// Get client IP (from request headers in API route)
export function getClientIP(req: any): string {
  return req.headers['x-forwarded-for']?.split(',')[0] || 
         req.headers['x-real-ip'] || 
         req.connection?.remoteAddress || 
         req.socket?.remoteAddress ||
         '127.0.0.1'
}

// Enhanced client-side IP detection for better match quality
export async function getClientIPAddress(): Promise<string> {
  if (typeof window === 'undefined') return '127.0.0.1'
  
  try {
    // Try to get IP from public service
    const response = await fetch('https://api.ipify.org?format=json', {
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    })
    
    if (response.ok) {
      const data = await response.json()
      if (data.ip) return data.ip
    }
  } catch (error) {
    console.warn('Could not fetch public IP:', error)
  }
  
  return '127.0.0.1'
}

// Enhanced user agent detection for better match quality
export function getEnhancedUserAgent(): string {
  if (typeof window === 'undefined') return 'unknown'
  
  // Get the most complete user agent string available
  return navigator.userAgent || navigator.appName || 'unknown'
}

// Check if user has given marketing consent
function hasMarketingConsent(): boolean {
  if (typeof window === 'undefined') return false
  
  const consentValue = document.cookie
    .split('; ')
    .find(row => row.startsWith('cookie_consent='))
    ?.split('=')[1]
  
  return consentValue === 'all'
}

// Facebook Pixel tracking (client-side) - with consent check
export function trackPixelEvent(eventName: string, eventId: string, customData: any = {}) {
  if (typeof window !== 'undefined' && window.fbq && hasMarketingConsent()) {
    try {
      window.fbq('track', eventName, customData, { eventID: eventId })
      console.log(`Facebook Pixel: ${eventName} tracked with ID: ${eventId}`, customData)
    } catch (error) {
      console.error('Facebook Pixel tracking error:', error)
    }
  } else {
    console.log(`Facebook Pixel: ${eventName} not tracked - consent required or fbq not available`)
  }
}

// CAPI Event Data Structure
export interface CAPIEventData {
  event_name: string
  event_time: number
  event_id: string
  action_source: 'website'
  event_source_url: string
  user_data: {
    client_ip_address: string
    client_user_agent: string
    em?: string[]
    ph?: string[]
    fbc?: string
    fbp?: string
    external_id?: string
  }
  custom_data?: {
    currency?: string
    value?: number
    content_name?: string
    content_category?: string
  }
}

// Send event to CAPI (server-side)
export async function sendCAPIEvent(eventData: CAPIEventData) {
  try {
    const response = await fetch('/api/facebook-capi', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ eventData })
    })

    if (!response.ok) {
      console.error('CAPI event failed:', response.statusText)
      return false
    }

    const result = await response.json()
    console.log('CAPI event sent successfully:', result)
    return true
  } catch (error) {
    console.error('CAPI event error:', error)
    return false
  }
}

// Track PageView event (both Pixel and CAPI) - Enhanced
export async function trackPageView(url: string, userAgent?: string, clientIP?: string) {
  if (!hasMarketingConsent()) {
    console.log('PageView tracking skipped - no marketing consent')
    return
  }

  const eventId = generateEventId()
  const eventTime = Math.floor(Date.now() / 1000)

  // Client-side Pixel tracking
  trackPixelEvent(FB_EVENTS.PAGE_VIEW, eventId)

  // Enhanced user data collection
  const enhancedUserAgent = userAgent || getEnhancedUserAgent()
  const enhancedIP = clientIP || await getClientIPAddress()

  // Server-side CAPI tracking with enhanced data
  const eventData: CAPIEventData = {
    event_name: FB_EVENTS.PAGE_VIEW,
    event_time: eventTime,
    event_id: eventId,
    action_source: 'website',
    event_source_url: url,
    user_data: {
      client_ip_address: enhancedIP,
      client_user_agent: enhancedUserAgent,
      // Enhanced Facebook cookie collection
      fbc: getEnhancedFacebookClickId(),
      fbp: getEnhancedFacebookBrowserId(),
      external_id: getExternalId()
    }
  }

  await sendCAPIEvent(eventData)
}

// Track Contact event (both Pixel and CAPI) - Enhanced
export async function trackContact(
  source: string = 'Unknown', 
  url?: string, 
  userAgent?: string, 
  clientIP?: string, 
  email?: string, 
  phone?: string
) {
  if (!hasMarketingConsent()) {
    console.log('Contact tracking skipped - no marketing consent')
    return
  }

  const eventId = generateEventId()
  const eventTime = Math.floor(Date.now() / 1000)
  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : '')
  
  // Enhanced user data collection
  const enhancedUserAgent = userAgent || getEnhancedUserAgent()
  const enhancedIP = clientIP || await getClientIPAddress()

  // Client-side Pixel tracking
  trackPixelEvent(FB_EVENTS.CONTACT, eventId, {
    content_name: source,
    content_category: 'Contact'
  })

  // Server-side CAPI tracking with enhanced data
  const userData: CAPIEventData['user_data'] = {
    client_ip_address: enhancedIP,
    client_user_agent: enhancedUserAgent,
    fbc: getEnhancedFacebookClickId(),
    fbp: getEnhancedFacebookBrowserId(),
    external_id: getExternalId()
  }

  // Add hashed email if provided
  if (email) {
    userData.em = [await hashEmail(email)]
  }

  // Add hashed phone if provided  
  if (phone) {
    userData.ph = [await hashPhone(phone)]
  }

  const eventData: CAPIEventData = {
    event_name: FB_EVENTS.CONTACT,
    event_time: eventTime,
    event_id: eventId,
    action_source: 'website',
    event_source_url: currentUrl,
    user_data: userData,
    custom_data: {
      content_name: source,
      content_category: 'Contact'
    }
  }

  await sendCAPIEvent(eventData)
}

// Quick tracking functions for common actions - Enhanced
export const FacebookTracking = {
  // CTA Button tracking
  trackStartNow: () => trackContact('CTA Start Nu Button'),
  trackPlanConversation: () => trackContact('CTA Plan Conversation Button'),
  trackStartProject: () => trackContact('Portfolio Start Project Button'),
  trackChatButton: () => trackContact('Floating Chat Button'),
  trackNavContact: () => trackContact('Navigation Contact'),
  
  // Hero section CTA buttons
  trackViewWork: () => trackContact('Hero View Work Button'),
  trackPlanMeeting: () => trackContact('Hero Plan Meeting Button'),
  
  // Custom tracking
  trackCustomContact: (source: string) => trackContact(source)
}

// Enhanced Facebook Click ID collection - Better match quality
function getEnhancedFacebookClickId(): string | undefined {
  if (typeof window === 'undefined') return undefined
  
  try {
    // Check URL parameter first (most reliable)
    const urlParams = new URLSearchParams(window.location.search)
    const fbclid = urlParams.get('fbclid')
    if (fbclid) {
      const timestamp = Date.now()
      const fbc = `fb.1.${timestamp}.${fbclid}`
      
      // Store in cookie for future use (7 days)
      document.cookie = `_fbc=${fbc}; path=/; max-age=604800; secure; samesite=lax`
      
      return fbc
    }

    // Check existing cookie
    const cookies = document.cookie.split(';')
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=')
      if (name === '_fbc' && value) {
        return decodeURIComponent(value)
      }
    }
    
    // Try to get from sessionStorage (backup)
    const sessionFbc = sessionStorage.getItem('_fbc')
    if (sessionFbc) {
      return sessionFbc
    }
    
  } catch (error) {
    console.warn('Could not retrieve Facebook Click ID:', error)
  }
  
  return undefined
}

// Enhanced Facebook Browser ID collection - Better match quality
function getEnhancedFacebookBrowserId(): string | undefined {
  if (typeof window === 'undefined') return undefined
  
  try {
    // Check cookie first
    const cookies = document.cookie.split(';')
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=')
      if (name === '_fbp' && value) {
        return decodeURIComponent(value)
      }
    }
    
    // If no cookie exists, try to create one (Facebook SDK should handle this)
    // Check if Facebook pixel has set the browser ID
    if (typeof window.fbq === 'function') {
      try {
        // Try to trigger pixel to set _fbp cookie
        window.fbq('init', '747224424447627') // Our pixel ID
        
        // Re-check cookie after init
        const newCookies = document.cookie.split(';')
        for (const cookie of newCookies) {
          const [name, value] = cookie.trim().split('=')
          if (name === '_fbp' && value) {
            return decodeURIComponent(value)
          }
        }
      } catch (error) {
        console.warn('Could not initialize Facebook pixel for _fbp:', error)
      }
    }
    
    // Try to get from localStorage (backup)
    const localFbp = localStorage.getItem('_fbp')
    if (localFbp) {
      return localFbp
    }
    
  } catch (error) {
    console.warn('Could not retrieve Facebook Browser ID:', error)
  }
  
  return undefined
}

// Generate External ID for better event matching
function getExternalId(): string | undefined {
  if (typeof window === 'undefined') return undefined
  
  try {
    // Check if we have a stored external ID
    let externalId = localStorage.getItem('fb_external_id')
    
    if (!externalId) {
      // Generate a new external ID (UUID-based)
      externalId = generateEventId()
      localStorage.setItem('fb_external_id', externalId)
    }
    
    return externalId
  } catch (error) {
    console.warn('Could not generate/retrieve external ID:', error)
    return undefined
  }
} 