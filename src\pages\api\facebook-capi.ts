import type { NextApiRequest, NextApiResponse } from 'next'
import { CAPIEventData, getClientIP } from '@/utils/facebook-tracking'

interface CAPIRequest {
  eventData: CAPIEventData
}

interface CAPIResponse {
  success: boolean
  message: string
  error?: string
}

// Facebook CAPI Configuration
const FACEBOOK_PIXEL_ID = process.env.FACEBOOK_PIXEL_ID || '747224424447627'
const FACEBOOK_ACCESS_TOKEN = process.env.FACEBOOK_ACCESS_TOKEN
const FACEBOOK_GRAPH_API_VERSION = process.env.FACEBOOK_GRAPH_API_VERSION || 'v19.0'

// Validate required environment variables
if (!FACEBOOK_ACCESS_TOKEN) {
  console.error('FACEBOOK_ACCESS_TOKEN environment variable is required')
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CAPIResponse>
) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed',
      error: 'Only POST requests allowed' 
    })
  }

  try {
    // Check if access token is configured
    if (!FACEBOOK_ACCESS_TOKEN) {
      return res.status(500).json({
        success: false,
        message: 'Facebook access token not configured',
        error: 'FACEBOOK_ACCESS_TOKEN environment variable is missing'
      })
    }

    const { eventData }: CAPIRequest = req.body

    if (!eventData) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request format',
        error: 'Event data required'
      })
    }

    // Enhance event data with server-side information
    const enhancedEventData = {
      ...eventData,
      user_data: {
        ...eventData.user_data,
        client_ip_address: getClientIP(req),
        client_user_agent: req.headers['user-agent'] || 'unknown'
      }
    }

    // Prepare CAPI payload
    const capiPayload = {
      data: [enhancedEventData],
      test_event_code: process.env.NODE_ENV === 'development' ? 
        (process.env.FACEBOOK_TEST_EVENT_CODE || 'TEST12345') : undefined
    }

    // Send to Facebook Conversions API
    const facebookResponse = await fetch(
      `https://graph.facebook.com/${FACEBOOK_GRAPH_API_VERSION}/${FACEBOOK_PIXEL_ID}/events`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${FACEBOOK_ACCESS_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(capiPayload)
      }
    )

    if (!facebookResponse.ok) {
      const errorData = await facebookResponse.text()
      console.error('Facebook CAPI error:', facebookResponse.status, errorData)
      
      return res.status(500).json({ 
        success: false,
        message: 'Failed to send event to Facebook',
        error: `Facebook API error: ${facebookResponse.status}` 
      })
    }

    const responseData = await facebookResponse.json()
    
    // Log success in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Facebook CAPI event sent successfully:', {
        event_name: eventData.event_name,
        event_id: eventData.event_id,
        events_received: responseData.events_received,
        messages: responseData.messages
      })
    }

    return res.status(200).json({ 
      success: true,
      message: 'Event sent successfully to Facebook CAPI' 
    })

  } catch (error) {
    console.error('CAPI API error:', error)
    return res.status(500).json({ 
      success: false,
      message: 'Internal server error',
      error: 'Failed to process CAPI request' 
    })
  }
} 