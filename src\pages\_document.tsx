import { Html, <PERSON>, Main, NextScript } from 'next/document'

export default function Document() {
  return (
    <Html lang="en" className="dark">
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-touch-fullscreen" content="yes" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Google Fonts */}
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet" />
        
        {/* Facebook Pixel - Consent-aware initialization */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              
              // Initialize with consent revoked by default
              fbq('consent', 'revoke');
              fbq('init', '747224424447627');
              
              // Check for existing consent
              function checkConsent() {
                const consentValue = document.cookie
                  .split('; ')
                  .find(row => row.startsWith('cookie_consent='))
                  ?.split('=')[1];
                
                if (consentValue === 'all') {
                  fbq('consent', 'grant');
                  fbq('track', 'PageView');
                  console.log('Facebook Pixel: Consent granted, PageView tracked');
                } else {
                  console.log('Facebook Pixel: Consent not granted, waiting for user action');
                }
              }
              
              // Check consent on load
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', checkConsent);
              } else {
                checkConsent();
              }
              
              // Listen for consent changes
              window.addEventListener('cookieConsentUpdate', function(event) {
                if (event.detail.consent === 'all') {
                  fbq('consent', 'grant');
                  fbq('track', 'PageView');
                  console.log('Facebook Pixel: Consent updated - granted');
                } else {
                  fbq('consent', 'revoke');
                  console.log('Facebook Pixel: Consent updated - revoked');
                }
              });
              
              // Make fbq globally available for tracking
              window.fbq = fbq;
            `,
          }}
        />
        <noscript>
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            height="1"
            width="1"
            style={{ display: 'none' }}
            src="https://www.facebook.com/tr?id=747224424447627&ev=PageView&noscript=1"
            alt=""
          />
        </noscript>
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  )
} 